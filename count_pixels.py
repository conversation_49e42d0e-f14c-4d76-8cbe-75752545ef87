#!/usr/bin/env python3
"""
Python script to find the total number of pixels in an image.
Supports multiple methods using PIL and OpenCV.
"""

from PIL import Image
import cv2
import os
import sys


def count_pixels_pil(image_path):
    """
    Count total pixels using PIL (Python Imaging Library).
    
    Args:
        image_path (str): Path to the image file
        
    Returns:
        tuple: (width, height, total_pixels)
    """
    try:
        with Image.open(image_path) as img:
            width, height = img.size
            total_pixels = width * height
            return width, height, total_pixels
    except Exception as e:
        print(f"Error opening image with PIL: {e}")
        return None, None, None


def count_pixels_opencv(image_path):
    """
    Count total pixels using OpenCV.
    
    Args:
        image_path (str): Path to the image file
        
    Returns:
        tuple: (width, height, total_pixels)
    """
    try:
        img = cv2.imread(image_path)
        if img is None:
            print("Error: Could not load image with OpenCV")
            return None, None, None
        
        height, width = img.shape[:2]
        total_pixels = width * height
        return width, height, total_pixels
    except Exception as e:
        print(f"Error opening image with OpenCV: {e}")
        return None, None, None


def get_image_info(image_path):
    """
    Get comprehensive image information including pixel count.
    
    Args:
        image_path (str): Path to the image file
    """
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found.")
        return
    
    print(f"Analyzing image: {image_path}")
    print("-" * 50)
    
    # Method 1: Using PIL
    print("Method 1: Using PIL (Pillow)")
    width_pil, height_pil, pixels_pil = count_pixels_pil(image_path)
    if pixels_pil is not None:
        print(f"  Width: {width_pil} pixels")
        print(f"  Height: {height_pil} pixels")
        print(f"  Total pixels: {pixels_pil:,}")
        
        # Additional PIL info
        try:
            with Image.open(image_path) as img:
                print(f"  Image mode: {img.mode}")
                print(f"  Image format: {img.format}")
        except:
            pass
    
    print()
    
    # Method 2: Using OpenCV
    print("Method 2: Using OpenCV")
    width_cv, height_cv, pixels_cv = count_pixels_opencv(image_path)
    if pixels_cv is not None:
        print(f"  Width: {width_cv} pixels")
        print(f"  Height: {height_cv} pixels")
        print(f"  Total pixels: {pixels_cv:,}")
        
        # Additional OpenCV info
        try:
            img = cv2.imread(image_path)
            if img is not None:
                if len(img.shape) == 3:
                    print(f"  Channels: {img.shape[2]}")
                print(f"  Data type: {img.dtype}")
        except:
            pass
    
    print()
    
    # File size information
    try:
        file_size = os.path.getsize(image_path)
        print(f"File size: {file_size:,} bytes ({file_size / (1024*1024):.2f} MB)")
    except:
        pass


def main():
    """Main function to handle command line arguments or interactive input."""
    
    if len(sys.argv) > 1:
        # Use command line argument
        image_path = sys.argv[1]
    else:
        # Interactive input
        image_path = input("Enter the path to your image file: ").strip()
        
        # Remove quotes if present
        if image_path.startswith('"') and image_path.endswith('"'):
            image_path = image_path[1:-1]
        elif image_path.startswith("'") and image_path.endswith("'"):
            image_path = image_path[1:-1]
    
    get_image_info(image_path)


if __name__ == "__main__":
    print("Image Pixel Counter")
    print("==================")
    print()
    
    # Check if required libraries are available
    try:
        import PIL
        print("✓ PIL (Pillow) is available")
    except ImportError:
        print("✗ PIL (Pillow) not found. Install with: pip install Pillow")
    
    try:
        import cv2
        print("✓ OpenCV is available")
    except ImportError:
        print("✗ OpenCV not found. Install with: pip install opencv-python")
    
    print()
    
    main()
